export default () => ({
  'users-permissions': {
    config: {
      register: {
        allowedFields: ['username', 'email', 'password', 'firstName', 'lastName', 'phone', 'company'],
      },
    },
  },
  upload: {
    config: {
      sizeLimit: 10 * 1024 * 1024, // 10MB
      breakpoints: {
        xlarge: 1920,
        large: 1000,
        medium: 750,
        small: 500,
        xsmall: 64
      },
      // Enable responsive images
      responsiveDimensions: true,
      // Add provider options for folder structure
      providerOptions: {
        localServer: {
          // Enable folder structure
          useFolderStructure: true,
        },
      },
      // Simple folder structure for backend organization
      // Strapi's built-in media library will handle folder creation and management
      // Optimize images
      actionOptions: {
        upload: {},
        uploadStream: {},
        delete: {},
      },
    },
  },
  graphql: {
    config: {
      endpoint: '/graphql',
      shadowCRUD: true,
      playgroundAlways: false,
      depthLimit: 7,
      amountLimit: 100,
      apolloServer: {
        tracing: false,
      },
    },
  },
  chartbrew: {
    enabled: true,
    config: {
      // Updated with correct Docker container IP
      // apiUrl: 'http://127.0.0.1:4019',  // Chartbrew container IP from docker inspect
      // frontendUrl: 'http://127.0.0.1:4018',
      // Alternative localhost config (if using host networking)
      apiUrl: 'http://***********:4019',
      frontendUrl: 'http://***********:4018',
    }
  },
});

