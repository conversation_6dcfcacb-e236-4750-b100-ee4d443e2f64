import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';

// Create axios instance
export const api = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('jwt');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('jwt');
      localStorage.removeItem('user');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (identifier: string, password: string) => {
    const response = await api.post('/auth/local', {
      identifier,
      password,
    });
    return response.data;
  },

  register: async (username: string, email: string, password: string) => {
    const response = await api.post('/auth/local/register', {
      username,
      email,
      password,
    });
    return response.data;
  },

  me: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },

  forgotPassword: async (email: string) => {
    const response = await api.post('/auth/forgot-password', {
      email,
    });
    return response.data;
  },

  resetPassword: async (code: string, password: string, passwordConfirmation: string) => {
    const response = await api.post('/auth/reset-password', {
      code,
      password,
      passwordConfirmation,
    });
    return response.data;
  },
};

// Properties API
export const propertiesAPI = {
  getAll: async (params?: any) => {
    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner', 'agent', 'project'],
        ...params
      }
    });
    return response.data;
  },

  getById: async (id: string, params?: any) => {
    const response = await api.get(`/properties/${id}`, {
      params: {
        populate: ['images', 'floorPlan', 'owner', 'agent', 'project'],
        ...params
      }
    });
    return response.data;
  },

  getBySlug: async (slug: string, params?: any) => {
    const response = await api.get(`/properties/by-slug/${slug}`, {
      params: {
        populate: ['images', 'floorPlan', 'owner', 'agent', 'project'],
        ...params
      }
    });
    return response.data;
  },

  getForEdit: async (id: string) => {
    const response = await api.get(`/properties/${id}/edit`);
    console.log('getForEdit API response:', response.data);
    return response.data;
  },

  getFeatured: async () => {
    const response = await api.get('/properties/featured');
    return response.data;
  },

  search: async (searchParams: any) => {
    const filters: any = {};

    // Handle search query (from search input)
    if (searchParams.search) {
      filters.$or = [
        { title: { $containsi: searchParams.search } },
        { city: { $containsi: searchParams.search } },
        { address: { $containsi: searchParams.search } },
        { neighborhood: { $containsi: searchParams.search } },
        { description: { $containsi: searchParams.search } }
      ];
    }

    // Handle location filter (from location dropdown)
    if (searchParams.location) {
      filters.$or = [
        { city: { $containsi: searchParams.location } },
        { address: { $containsi: searchParams.location } },
        { neighborhood: { $containsi: searchParams.location } }
      ];
    }

    if (searchParams.propertyType) {
      // Handle both single and multiple property types
      if (Array.isArray(searchParams.propertyType)) {
        filters.propertyType = { $in: searchParams.propertyType };
      } else if (searchParams.propertyType.includes(',')) {
        filters.propertyType = { $in: searchParams.propertyType.split(',') };
      } else {
        filters.propertyType = searchParams.propertyType;
      }
    }

    if (searchParams.offer) {
      filters.offer = searchParams.offer;
    }

    // Handle both old and new parameter names for backward compatibility
    const minPrice = searchParams.priceMin || searchParams.minPrice;
    const maxPrice = searchParams.priceMax || searchParams.maxPrice;

    if (minPrice) {
      filters.price = { $gte: parseFloat(minPrice) };
    }

    if (maxPrice) {
      filters.price = { ...filters.price, $lte: parseFloat(maxPrice) };
    }

    if (searchParams.bedrooms) {
      filters.bedrooms = { $gte: parseInt(searchParams.bedrooms) };
    }

    if (searchParams.bathrooms) {
      filters.bathrooms = { $gte: parseInt(searchParams.bathrooms) };
    }

    // Handle area range filtering
    const minArea = searchParams.minArea;
    const maxArea = searchParams.maxArea;

    if (minArea) {
      filters.area = { $gte: parseFloat(minArea) };
    }

    if (maxArea) {
      filters.area = { ...filters.area, $lte: parseFloat(maxArea) };
    }

    if (searchParams.city) {
      filters.city = { $containsi: searchParams.city };
    }

    if (searchParams.neighborhood) {
      filters.neighborhood = { $containsi: searchParams.neighborhood };
    }

    if (searchParams.propertyCode) {
      filters.propertyCode = { $containsi: searchParams.propertyCode };
    }

    if (searchParams.isLuxury) {
      filters.isLuxury = true;
    }

    if (searchParams.furnished) {
      filters.furnished = true;
    }

    if (searchParams.petFriendly) {
      filters.petFriendly = true;
    }

    if (searchParams.parking) {
      filters.parking = { $gte: parseInt(searchParams.parking) };
    }

    if (searchParams.yearBuilt) {
      filters.yearBuilt = { $gte: parseInt(searchParams.yearBuilt) };
    }

    if (searchParams.features && searchParams.features.length > 0) {
      // Handle features array - property should have all selected features
      const featuresArray = Array.isArray(searchParams.features)
        ? searchParams.features
        : searchParams.features.split(',');

      if (featuresArray.length > 0) {
        filters.features = { $containsi: featuresArray };
      }
    }

    // Build sort parameter
    let sort = ['createdAt:desc']; // default sort
    if (searchParams.sortBy) {
      const sortOrder = searchParams.sortOrder || 'desc';
      sort = [`${searchParams.sortBy}:${sortOrder}`];

      // Add secondary sort for consistency
      if (searchParams.sortBy !== 'createdAt') {
        sort.push('createdAt:desc');
      }
    }

    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner', 'agent', 'project'],
        filters,
        sort,
        pagination: {
          page: searchParams.page || 1,
          pageSize: searchParams.pageSize || 12
        }
      }
    });
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/properties/${id}`, {
      params: {
        populate: ['images', 'owner', 'agent', 'project', 'floorPlan']
      }
    });
    return response.data;
  },

  create: async (propertyData: any) => {
    const response = await api.post('/properties', {
      data: propertyData
    });
    return response.data;
  },

  update: async (id: string, propertyData: any) => {
    const response = await api.put(`/properties/${id}`, {
      data: propertyData
    });
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/properties/${id}`);
    return response.data;
  },

  publish: async (id: string) => {
    const response = await api.put(`/properties/${id}`, {
      data: {
        publishedAt: new Date().toISOString()
      }
    });
    return response.data;
  },

  unpublish: async (id: string) => {
    const response = await api.put(`/properties/${id}`, {
      data: {
        publishedAt: null
      }
    });
    return response.data;
  },

  getMyProperties: async (params?: any) => {
    try {
      const response = await api.get('/properties/my-properties', {
        params: {
          populate: ['images', 'project'],
          pagination: {
            page: params?.page || 1,
            pageSize: params?.pageSize || 12
          },
          sort: params?.sort || ['createdAt:desc'],
          filters: params?.filters || {},
          ...params
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching my properties:', error);
      throw error;
    }
  },

  getCities: async () => {
    const response = await api.get('/properties', {
      params: {
        fields: ['city'],
        pagination: { limit: -1 }
      }
    });
    const cities = [...new Set(response.data.data.map((p: any) => p.city))];
    return cities;
  },

  getNeighborhoods: async (city?: string) => {
    const filters = city ? { city: { $eq: city } } : {};
    const response = await api.get('/properties', {
      params: {
        fields: ['neighborhood'],
        filters,
        pagination: { limit: -1 }
      }
    });
    const neighborhoods = [...new Set(response.data.data.map((p: any) => p.neighborhood).filter(Boolean))];
    return neighborhoods;
  },

  // Get features options from backend enum
  getFeaturesOptions: async (): Promise<{data: import('@/types/api').FeatureOption[]}> => {
    const response = await api.get('/properties/features-options');
    return response.data;
  },

};

// Projects API
export const projectsAPI = {
  getAll: async (params?: any) => {
    const response = await api.get('/projects', {
      params: {
        populate: ['images', 'properties'],
        ...params
      }
    });
    return response.data;
  },

  getFeatured: async () => {
    const response = await api.get('/projects', {
      params: {
        populate: ['images', 'properties'],
        filters: {
          featured: true,
          publishedAt: { $notNull: true }
        },
        sort: ['createdAt:desc'],
        pagination: {
          limit: 4
        }
      }
    });
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/projects/${id}`, {
      params: {
        populate: ['images', 'properties', 'floorPlans', 'brochure']
      }
    });
    return response.data;
  },

  getProperties: async (id: string) => {
    const response = await api.get(`/projects/${id}/properties`);
    return response.data;
  },
};

// Messages API
export const messagesAPI = {
  getAll: async () => {
    const response = await api.get('/messages');
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/messages/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await api.post('/messages', { data });
    return response.data;
  },

  getInbox: async () => {
    const response = await api.get('/messages/inbox');
    return response.data;
  },

  getSent: async () => {
    const response = await api.get('/messages/sent');
    return response.data;
  },

  markAsRead: async (id: string) => {
    const response = await api.put(`/messages/${id}/mark-as-read`);
    return response.data;
  },
};

// Notifications API
export const notificationsAPI = {
  getAll: async (params?: { page?: number; pageSize?: number; unreadOnly?: boolean }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params?.unreadOnly) queryParams.append('unreadOnly', 'true');

    const response = await api.get(`/notifications?${queryParams.toString()}`);
    return response.data;
  },

  getUnreadCount: async () => {
    const response = await api.get('/notifications/unread-count');
    return response.data;
  },

  markAsRead: async (id: string) => {
    const response = await api.put(`/notifications/${id}/mark-as-read`);
    return response.data;
  },

  markAllAsRead: async () => {
    const response = await api.put('/notifications/mark-all-as-read');
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  }
};

// Membership API
export const membershipAPI = {
  getAll: async () => {
    const response = await api.get('/memberships');
    return response.data;
  },

  getMyMembership: async () => {
    const response = await api.get('/memberships/my-membership');
    return response.data;
  },

  subscribe: async (membershipId: string) => {
    const response = await api.post('/memberships/subscribe', {
      membershipId
    });
    return response.data;
  },
};

// Upload API
export const uploadAPI = {
  upload: async (files: FileList) => {
    const formData = new FormData();
    Array.from(files).forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// Export default api instance for custom hooks
export default api;
