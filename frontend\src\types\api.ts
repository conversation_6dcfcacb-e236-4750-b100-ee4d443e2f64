// Strapi API Response Types
export interface StrapiResponse<T> {
  data: T;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiEntity {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Property Types
export interface Property extends StrapiEntity {
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  offer: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood?: string;
  coordinates?: { lat: number; lng: number };
  nearbyPlaces?: any;
  propertyCode?: string;
  isLuxury?: boolean;
  features?: string[]; // Keep for backward compatibility during transition
  featuresEnum?: string[]; // New enum field
  views: number;
  images?: StrapiMedia[];
  floorPlan?: StrapiMedia[];
  virtualTour?: string;
  yearBuilt?: number;
  parking?: number;
  furnished?: boolean;
  petFriendly?: boolean;
  owner?: User;
  agent?: User;
  project?: Project;
  slug?: string;
}

// Project Types
export interface Project extends StrapiEntity {
  name: string;
  description: string;
  developer: string;
  status: string;
  projectType: string;
  totalUnits?: number;
  completionDate?: string;
  startingPrice?: number;
  location: string;
  coordinates?: { lat: number; lng: number };
  amenities?: string[];
  images?: StrapiMedia[];
  floorPlans?: StrapiMedia[];
  brochure?: StrapiMedia;
  properties?: Property[];
  featured?: boolean;
  slug?: string;
}

// User Types
export interface User extends StrapiEntity {
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  company?: string;
  role?: {
    id: number;
    name: string;
    type: string;
  };
}

// Media Types
export interface StrapiMedia {
  id: number;
  documentId: string;
  name: string;
  alternativeText?: string;
  caption?: string;
  width: number;
  height: number;
  formats?: {
    thumbnail?: MediaFormat;
    small?: MediaFormat;
    medium?: MediaFormat;
    large?: MediaFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  createdAt: string;
  updatedAt: string;
}

export interface MediaFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  width: number;
  height: number;
  size: number;
  url: string;
}

// Message Types
export interface Message extends StrapiEntity {
  subject: string;
  content: string;
  sender: User;
  recipient: User;
  property?: Property;
  isRead: boolean;
  messageType: 'inquiry' | 'general' | 'viewing_request';
}

// Notification Types
export interface Notification extends StrapiEntity {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  user: User;
  relatedEntity?: {
    type: string;
    id: string;
  };
}

// Membership Types
export interface Membership extends StrapiEntity {
  name: string;
  description: string;
  price: number;
  duration: number; // in days
  features: string[];
  isActive: boolean;
}

export interface UserMembership extends StrapiEntity {
  user: User;
  membership: Membership;
  startDate: string;
  endDate: string;
  isActive: boolean;
}

// Search and Filter Types
export interface PropertyFilters {
  location?: string;
  propertyType?: string;
  offer?: string;
  priceMin?: number;
  priceMax?: number;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  city?: string;
  neighborhood?: string;
  propertyCode?: string;
  isLuxury?: boolean;
  page?: number;
  pageSize?: number;
}

// Feature option type for dynamic loading
export interface FeatureOption {
  value: string;
  label: string;
}

export interface ProjectFilters {
  projectType?: string;
  status?: string;
  developer?: string;
  location?: string;
  page?: number;
  pageSize?: number;
}

// Auth Types
export interface LoginCredentials {
  identifier: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  jwt: string;
  user: User;
}

// API Error Types
export interface ApiError {
  message: string;
  status: number;
  name: string;
  details?: any;
}
