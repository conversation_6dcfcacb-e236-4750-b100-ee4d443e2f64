# Property Features Migration Script: JSON to Enum
# Migrates existing property features from JSON format to new enum field

param(
    [string]$ApiUrl = "http://localhost:1337/api",
    [string]$AdminEmail = "<EMAIL>",
    [string]$AdminPassword = "your-admin-password",
    [switch]$DryRun = $false
)

# Define mapping from frontend display names to enum values
$featureMapping = @{
    "Swimming Pool" = "swimming-pool"
    "Gym" = "gym"
    "Garden" = "garden"
    "Balcony" = "balcony"
    "Terrace" = "terrace"
    "Garage" = "garage"
    "Security" = "security"
    "Elevator" = "elevator"
    "Air Conditioning" = "air-conditioning"
    "Heating" = "heating"
    "Fireplace" = "fireplace"
    "Walk-in Closet" = "walk-in-closet"
    "Storage Room" = "storage-room"
    "Laundry Room" = "laundry-room"
    "Maid Room" = "maid-room"
    "WiFi" = "wifi"
    "View" = "view"
    "Concierge" = "concierge"
    "Shopping" = "shopping"
    "Cafe" = "cafe"
    "Parking" = "parking"
    # Additional mappings for filter component values
    "pool" = "swimming-pool"
    "gym" = "gym"
    "security" = "security"
    "garden" = "garden"
    "view" = "view"
    "parking" = "parking"
    "elevator" = "elevator"
    "ac" = "air-conditioning"
    "heating" = "heating"
    "balcony" = "balcony"
    "terrace" = "terrace"
    "storage" = "storage-room"
    "laundry" = "laundry-room"
    "concierge" = "concierge"
    "wifi" = "wifi"
}

Write-Host "🚀 Starting Property Features Migration" -ForegroundColor Green
Write-Host "   API URL: $ApiUrl" -ForegroundColor Gray
Write-Host "   Dry Run: $DryRun" -ForegroundColor Gray
Write-Host ""

# Login to get auth token
try {
    Write-Host "🔐 Authenticating..." -ForegroundColor Yellow
    $loginResponse = Invoke-RestMethod -Uri "$ApiUrl/auth/local" -Method POST -Body (@{
        identifier = $AdminEmail
        password = $AdminPassword
    } | ConvertTo-Json) -ContentType "application/json"
    
    $authToken = $loginResponse.jwt
    Write-Host "✅ Authentication successful" -ForegroundColor Green
} catch {
    Write-Error "❌ Authentication failed: $($_.Exception.Message)"
    exit 1
}

# Fetch all properties with features
try {
    Write-Host "📊 Fetching properties..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "$ApiUrl/properties?pagination[pageSize]=1000&fields[0]=title&fields[1]=features&fields[2]=featuresEnum" -Headers @{
        Authorization = "Bearer $authToken"
    }
    
    $properties = $response.data
    Write-Host "📊 Found $($properties.Count) properties to analyze" -ForegroundColor Green
} catch {
    Write-Error "❌ Failed to fetch properties: $($_.Exception.Message)"
    exit 1
}

$successCount = 0
$errorCount = 0
$skippedCount = 0
$totalProcessed = 0

Write-Host ""
Write-Host "🔄 Processing properties..." -ForegroundColor Yellow

foreach ($property in $properties) {
    $totalProcessed++
    $title = $property.attributes.title
    $oldFeatures = $property.attributes.features
    $existingEnumFeatures = $property.attributes.featuresEnum
    
    # Skip if already has enum features or no old features
    if ($existingEnumFeatures -and $existingEnumFeatures.Count -gt 0) {
        Write-Host "⏭️  Skipping '$title' - already has enum features" -ForegroundColor Gray
        $skippedCount++
        continue
    }
    
    if (-not $oldFeatures -or $oldFeatures.Count -eq 0) {
        Write-Host "⏭️  Skipping '$title' - no features to migrate" -ForegroundColor Gray
        $skippedCount++
        continue
    }
    
    # Convert features to enum values
    $enumFeatures = @()
    $unmappedFeatures = @()
    
    foreach ($feature in $oldFeatures) {
        if ($featureMapping.ContainsKey($feature)) {
            $enumFeatures += $featureMapping[$feature]
        } else {
            $unmappedFeatures += $feature
            Write-Warning "⚠️  Unknown feature: '$feature' in property '$title'"
        }
    }
    
    if ($unmappedFeatures.Count -gt 0) {
        Write-Host "   Unmapped features: $($unmappedFeatures -join ', ')" -ForegroundColor Yellow
    }
    
    if ($enumFeatures.Count -eq 0) {
        Write-Host "⏭️  Skipping '$title' - no mappable features found" -ForegroundColor Gray
        $skippedCount++
        continue
    }
    
    # Prepare update data
    $updateData = @{
        data = @{
            featuresEnum = $enumFeatures
        }
    }
    
    if ($DryRun) {
        Write-Host "🔍 DRY RUN - Would update '$title':" -ForegroundColor Cyan
        Write-Host "   Old features: $($oldFeatures -join ', ')" -ForegroundColor Gray
        Write-Host "   New enum features: $($enumFeatures -join ', ')" -ForegroundColor Gray
        $successCount++
    } else {
        try {
            $updateResponse = Invoke-RestMethod -Uri "$ApiUrl/properties/$($property.documentId)" -Method PUT -Body ($updateData | ConvertTo-Json -Depth 3) -Headers @{
                Authorization = "Bearer $authToken"
                "Content-Type" = "application/json"
            }
            Write-Host "✅ Updated '$title' with $($enumFeatures.Count) features" -ForegroundColor Green
            $successCount++
        } catch {
            Write-Error "❌ Failed to update '$title': $($_.Exception.Message)"
            $errorCount++
        }
    }
}

Write-Host ""
Write-Host "📈 Migration Summary:" -ForegroundColor Green
Write-Host "   📊 Total properties: $totalProcessed" -ForegroundColor Gray
Write-Host "   ✅ Successfully migrated: $successCount" -ForegroundColor Green
Write-Host "   ❌ Failed migrations: $errorCount" -ForegroundColor Red
Write-Host "   ⏭️  Skipped: $skippedCount" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host ""
    Write-Host "🔍 This was a DRY RUN - no actual changes were made" -ForegroundColor Cyan
    Write-Host "   Run without -DryRun flag to perform actual migration" -ForegroundColor Cyan
}

if ($errorCount -eq 0 -and $successCount -gt 0) {
    Write-Host ""
    Write-Host "🎉 Migration completed successfully!" -ForegroundColor Green
} elseif ($errorCount -gt 0) {
    Write-Host ""
    Write-Host "⚠️ Migration completed with $errorCount errors. Please review the logs." -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "ℹ️ No properties required migration." -ForegroundColor Blue
}
